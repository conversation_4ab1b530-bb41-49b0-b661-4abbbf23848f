# VPN客户端生成器

这是一个功能强大的VPN客户端配置生成器，为您的Flutter VPN应用提供完整的客户端管理解决方案。

## 功能特性

### 🔧 客户端生成器
- **多协议支持**: OpenVPN、IKEv2、WireGuard
- **批量生成**: 一次性为多个客户端生成配置
- **自动证书生成**: 自动生成客户端证书和密钥
- **配置验证**: 自动验证配置的有效性
- **跨平台脚本**: 生成Linux、Windows、macOS安装脚本

### 📱 配置管理
- **本地存储**: 使用SharedPreferences安全存储配置
- **配置导入/导出**: 支持多种格式的配置文件
- **QR码生成**: 为移动设备生成QR码配置
- **活跃配置管理**: 轻松切换和管理活跃配置

### 🎨 用户界面
- **现代化UI**: 美观的Material Design界面
- **搜索功能**: 快速查找配置
- **统计信息**: 显示配置统计和状态
- **批量操作**: 支持批量导出和管理

## 快速开始

### 1. 安装依赖

确保在 `pubspec.yaml` 中添加了必要的依赖：

```yaml
dependencies:
  http: ^1.1.0
  shared_preferences: ^2.2.2
  path_provider: ^2.1.1
  crypto: ^3.0.3
  uuid: ^4.1.0
```

### 2. 使用客户端生成器

```dart
import 'package:etornam_vpn/services/vpn_client_generator.dart';

final generator = VpnClientGenerator.instance;

// 生成单个客户端配置
final result = await generator.generateClientPackage(
  clientName: 'John的手机',
  serverAddress: 'vpn.example.com',
  port: 1194,
  protocol: 'OpenVPN',
  customDns: '*******,*******',
  enableKillSwitch: true,
  enableAutoReconnect: true,
);

if (result['success']) {
  print('客户端配置生成成功！');
  print('客户端ID: ${result['clientInfo']['clientId']}');
}
```

### 3. 批量生成客户端

```dart
// 批量生成多个客户端配置
final clientNames = ['客户端1', '客户端2', '客户端3'];

final results = await generator.generateBatchClients(
  clientNames: clientNames,
  serverAddress: 'vpn.example.com',
  port: 1194,
  protocol: 'OpenVPN',
);

print('成功生成 ${results.where((r) => r['success']).length} 个配置');
```

### 4. 配置管理

```dart
import 'package:etornam_vpn/services/vpn_config_service.dart';

final configService = VpnConfigService.instance;

// 获取所有配置
final configs = await configService.getAllConfigs();

// 设置活跃配置
await configService.setActiveConfig(configId);

// 获取活跃配置
final activeConfig = await configService.getActiveConfig();

// 导出配置
final filePath = await generator.exportConfig(config, 'OpenVPN');
```

## 支持的协议

### OpenVPN
- 端口: 1194 (默认)
- 支持UDP/TCP协议
- 自动生成.ovpn配置文件
- 包含CA证书、客户端证书和私钥

### IKEv2
- 端口: 500 (默认)
- 支持EAP认证
- 适用于移动设备
- 快速重连能力

### WireGuard
- 端口: 51820 (默认)
- 现代化VPN协议
- 高性能和安全性
- 简洁的配置格式

## 生成的文件

每个客户端配置包含以下文件：

### 配置文件
- `client.ovpn` - OpenVPN配置文件
- `client.conf` - WireGuard配置文件
- `config.json` - 通用JSON配置

### 脚本文件
- `connect.sh` - 连接脚本
- `disconnect.sh` - 断开连接脚本
- `install_linux.sh` - Linux安装脚本
- `install_windows.bat` - Windows安装脚本
- `install_macos.sh` - macOS安装脚本

### 证书文件
- CA证书
- 客户端证书
- 私钥

## 高级功能

### 配置验证

```dart
final validation = await generator.validateClientConfig(config);

if (!validation['isValid']) {
  print('配置问题:');
  for (final issue in validation['issues']) {
    print('- $issue');
  }
}

if (validation['warnings'].isNotEmpty) {
  print('警告:');
  for (final warning in validation['warnings']) {
    print('- $warning');
  }
}
```

### QR码生成

```dart
final qrData = await generator.generateQrConfig(config);
// 使用qrData生成QR码供移动设备扫描
```

### 配置导入/导出

```dart
// 导出配置
final filePath = await generator.exportConfig(config, 'OpenVPN');

// 导入配置
final importedConfig = await configService.importConfig(filePath);
```

## 界面使用

### 客户端生成器页面
1. 在主页面点击右上角菜单
2. 选择"客户端生成器"
3. 填写客户端信息和服务器配置
4. 点击"生成客户端配置"

### 配置管理页面
1. 在主页面点击右上角菜单
2. 选择"配置管理"
3. 查看、编辑、导出或删除配置
4. 设置活跃配置

### 批量生成
1. 在客户端生成器页面
2. 点击"批量生成客户端"
3. 输入客户端名称列表
4. 一次性生成多个配置

## 安全考虑

- 所有密码使用安全随机生成器生成
- 证书和私钥安全存储
- 配置文件加密存储在本地
- 支持Kill Switch防止数据泄露

## 故障排除

### 常见问题

1. **生成失败**
   - 检查服务器地址格式
   - 确认端口号在有效范围内
   - 验证协议类型是否支持

2. **导出失败**
   - 检查存储权限
   - 确认磁盘空间充足
   - 验证文件路径有效性

3. **导入失败**
   - 检查文件格式是否正确
   - 确认文件内容完整
   - 验证配置参数有效性

### 调试模式

在开发过程中，可以启用调试输出：

```dart
// 在main.dart中添加
void main() {
  debugPrint('VPN客户端生成器调试模式已启用');
  runApp(MyApp());
}
```

## 贡献

欢迎提交Issue和Pull Request来改进这个客户端生成器！

## 许可证

本项目采用MIT许可证。
