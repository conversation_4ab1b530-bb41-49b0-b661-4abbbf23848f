# Etornam VPN

<p align="center">

[![Maintenance](https://img.shields.io/badge/Maintained%3F-yes-green.svg)](https://github.com/RegNex/EtornamVpnFlutter/graphs/commit-activity)
[![ForTheBadge built-with-love](http://ForTheBadge.com/images/badges/built-with-love.svg)](https://github.com/RegNex/)
[![ForTheBadge made-with-flutter](https://img.shields.io/badge/flutter-made%20with%20flutter-blue.svg)](https://flutter.dev)
[![ForTheBadge ios-supported](https://img.shields.io/badge/IOS-IOS%20Supported-lightgrey.svg)](https://flutter.dev)
[![ForTheBadge ios-android](https://img.shields.io/badge/android-android%20supported-green.svg)](https://flutter.dev)

</p>

This project is an implementation of a Design i found on [Uplabs.com](https://www.uplabs.com/posts/xten-vpn) by [Blackhole Design](https://www.uplabs.com/blackhole)

## Art

<img src="https://raw.githubusercontent.com/RegNex/EtornamVpnFlutter/master/screenshots/VPN.png" width="100%"  height="50%"/>
<br>
   
<tr>
    <td><img align="left" src="https://raw.githubusercontent.com/RegNex/EtornamVpnFlutter/master/screenshots/art_1.png" width="200" height="400"/></td>
    <td><img src="https://raw.githubusercontent.com/RegNex/EtornamVpnFlutter/master/screenshots/art_2.png" width="200" height="400"/></td> 
</tr>

## Getting Started

This project is a starting point for a Flutter application.

To clone this project,
open your terminal or cmd

```
cd folder/to/clone-into/
```

```
git clone https://github.com/RegNex/EtornamVpnFlutter.git
```

Then
locate the project on your system and open with android studio or Vscode or intellij IDE.

To Run:
```
C:\path\to\project> flutter pub get

```
then run:

```
C:\path\to\project> flutter run

```

## Build release version

```
run: flutter build <OS PLATFORM> e.g flutter build ios
```


## Switch Theme

**Automatically switch theme based on system settings**

## Resources

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://flutter.io/docs/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://flutter.io/docs/cookbook)

For help getting started with Flutter, view our
[online documentation](https://flutter.io/docs), which offers tutorials,
samples, guidance on mobile development, and a full API reference.

## Prerequisites

What things you need to run the app

```
* Android Studio/Vscode/Intellij IDE
* Flutter SDK
* Android SDK
* MacBook
```

## How to contribute

- **Fork the repository and clone it locally**. Connect your local to the original “upstream” repository by adding it as a remote. Pull in changes from “upstream” often so that you stay up to date so that when you submit your pull request, merge conflicts will be less likely. (See more detailed instructions here.)
- **Create a branch** for your edits.
- **Reference any relevant issues** or supporting documentation in your PR (for example, “Closes #37.”)
- **Include screenshots of the before and after** if your changes include differences in HTML/CSS. Drag and drop the images into the body of your pull request.
- **Test your changes!** Run your changes against any existing tests if they exist and create new ones when needed. Whether tests exist or not, make sure your changes don’t break the existing project.
- **Contribute in the style of the project** to the best of your abilities. This may mean using indents, semi-colons or comments differently than you would in your own repository, but makes it easier for the maintainer to merge, others to understand and maintain in the future.

## Built With

- [Android Studio](https://developer.android.com/studio/install) - How to install Android Studio
- [Flutter](https://flutter.io) - Flutter Official website

## Author 😊

**Etornam Sunu Bright**

- [**Twitter**](https://bit.ly/3ivb9GC)
- [**Linkedin**](https://bit.ly/3iyxOl8)

## Inspiration

**Blackhole Design**

- [**https://www.uplabs.com/blackhole**](https://www.uplabs.com/posts/xten-vpn)

## License

MIT License

Copyright (c) 2020 Etornam Sunu

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.


