import 'dart:convert';
import 'package:etornam_vpn/models/vpn_config.dart';
import 'package:etornam_vpn/services/vpn_client_generator.dart';
import 'package:etornam_vpn/services/vpn_config_service.dart';

/// VPN客户端生成器演示
void main() async {
  print('🔧 VPN客户端生成器演示');
  print('=' * 50);

  final generator = VpnClientGenerator.instance;
  final configService = VpnConfigService.instance;

  // 演示1: 生成单个OpenVPN客户端配置
  print('\n📱 演示1: 生成OpenVPN客户端配置');
  print('-' * 30);
  
  final openVpnResult = await generator.generateClientPackage(
    clientName: 'John的iPhone',
    serverAddress: 'vpn.example.com',
    port: 1194,
    protocol: 'OpenVPN',
    customDns: '*******,*******',
    enableKillSwitch: true,
    enableAutoReconnect: true,
  );

  if (openVpnResult['success']) {
    final clientInfo = openVpnResult['clientInfo'];
    print('✅ OpenVPN配置生成成功!');
    print('   客户端ID: ${clientInfo['clientId']}');
    print('   客户端名称: ${clientInfo['clientName']}');
    print('   服务器: ${clientInfo['serverAddress']}:${clientInfo['port']}');
    print('   协议: ${clientInfo['protocol']}');
    
    // 保存配置
    final vpnConfig = VpnConfig.fromJson(openVpnResult['vpnConfig']);
    await configService.saveConfig(vpnConfig);
    print('   配置已保存到本地存储');
    
    // 显示生成的配置文件
    final configFiles = openVpnResult['configFiles'] as Map<String, String>;
    print('   生成的配置文件:');
    configFiles.forEach((fileName, content) {
      print('     - $fileName (${content.length} 字符)');
    });
  } else {
    print('❌ OpenVPN配置生成失败: ${openVpnResult['error']}');
  }

  // 演示2: 生成WireGuard客户端配置
  print('\n🔒 演示2: 生成WireGuard客户端配置');
  print('-' * 30);
  
  final wireGuardResult = await generator.generateClientPackage(
    clientName: 'Sarah的笔记本',
    serverAddress: '*************',
    port: 51820,
    protocol: 'WireGuard',
    customDns: '*******,*******',
    enableKillSwitch: true,
    enableAutoReconnect: false,
  );

  if (wireGuardResult['success']) {
    final clientInfo = wireGuardResult['clientInfo'];
    print('✅ WireGuard配置生成成功!');
    print('   客户端ID: ${clientInfo['clientId']}');
    print('   客户端名称: ${clientInfo['clientName']}');
    print('   服务器: ${clientInfo['serverAddress']}:${clientInfo['port']}');
    print('   协议: ${clientInfo['protocol']}');
    
    // 保存配置
    final vpnConfig = VpnConfig.fromJson(wireGuardResult['vpnConfig']);
    await configService.saveConfig(vpnConfig);
    print('   配置已保存到本地存储');
  } else {
    print('❌ WireGuard配置生成失败: ${wireGuardResult['error']}');
  }

  // 演示3: 批量生成客户端配置
  print('\n👥 演示3: 批量生成客户端配置');
  print('-' * 30);
  
  final clientNames = [
    'Alice的Android',
    'Bob的Windows',
    'Charlie的Mac',
    'Diana的iPad'
  ];
  
  final batchResults = await generator.generateBatchClients(
    clientNames: clientNames,
    serverAddress: 'batch.vpn.example.com',
    port: 1194,
    protocol: 'OpenVPN',
    customDns: '*******,*******',
    enableKillSwitch: true,
    enableAutoReconnect: true,
  );

  final successCount = batchResults.where((r) => r['success']).length;
  print('✅ 批量生成完成: $successCount/${batchResults.length} 成功');
  
  for (int i = 0; i < batchResults.length; i++) {
    final result = batchResults[i];
    if (result['success']) {
      final clientInfo = result['clientInfo'];
      print('   ✓ ${clientInfo['clientName']} - ID: ${clientInfo['clientId'].substring(0, 8)}...');
      
      // 保存配置
      final vpnConfig = VpnConfig.fromJson(result['vpnConfig']);
      await configService.saveConfig(vpnConfig);
    } else {
      print('   ✗ ${clientNames[i]} - 失败: ${result['error']}');
    }
  }

  // 演示4: 配置管理
  print('\n📋 演示4: 配置管理');
  print('-' * 30);
  
  final allConfigs = await configService.getAllConfigs();
  print('📊 当前存储的配置总数: ${allConfigs.length}');
  
  if (allConfigs.isNotEmpty) {
    print('配置列表:');
    for (final config in allConfigs) {
      print('   • ${config.name} (${config.protocol}) - ${config.serverAddress}:${config.port}');
    }
    
    // 设置第一个配置为活跃配置
    final firstConfig = allConfigs.first;
    await configService.setActiveConfig(firstConfig.id);
    print('\n🎯 已设置活跃配置: ${firstConfig.name}');
    
    // 验证活跃配置
    final activeConfig = await configService.getActiveConfig();
    if (activeConfig != null) {
      print('✅ 当前活跃配置: ${activeConfig.name}');
    }
  }

  // 演示5: 配置验证
  print('\n🔍 演示5: 配置验证');
  print('-' * 30);
  
  if (allConfigs.isNotEmpty) {
    final configToValidate = allConfigs.first;
    final validation = await generator.validateClientConfig(configToValidate);
    
    print('验证配置: ${configToValidate.name}');
    print('验证结果: ${validation['isValid'] ? '✅ 有效' : '❌ 无效'}');
    
    if (validation['issues'].isNotEmpty) {
      print('问题:');
      for (final issue in validation['issues']) {
        print('   ❌ $issue');
      }
    }
    
    if (validation['warnings'].isNotEmpty) {
      print('警告:');
      for (final warning in validation['warnings']) {
        print('   ⚠️ $warning');
      }
    }
  }

  // 演示6: QR码生成
  print('\n📱 演示6: QR码配置生成');
  print('-' * 30);
  
  if (allConfigs.isNotEmpty) {
    final configForQr = allConfigs.first;
    try {
      final qrData = await generator.generateQrConfig(configForQr);
      print('✅ QR码数据已生成 (${qrData.length} 字符)');
      print('QR码数据预览: ${qrData.substring(0, 50)}...');
      print('💡 此数据可用于生成QR码供移动设备扫描导入');
    } catch (e) {
      print('❌ QR码生成失败: $e');
    }
  }

  // 演示7: 配置文件生成
  print('\n📄 演示7: 配置文件内容预览');
  print('-' * 30);
  
  if (allConfigs.isNotEmpty) {
    final config = allConfigs.firstWhere(
      (c) => c.protocol == 'OpenVPN',
      orElse: () => allConfigs.first,
    );
    
    print('OpenVPN配置文件内容预览:');
    print('─' * 40);
    final ovpnContent = config.generateOpenVpnConfig();
    final lines = ovpnContent.split('\n');
    for (int i = 0; i < lines.length && i < 10; i++) {
      print('${(i + 1).toString().padLeft(2)}: ${lines[i]}');
    }
    if (lines.length > 10) {
      print('... (还有 ${lines.length - 10} 行)');
    }
    
    // WireGuard配置预览
    final wireGuardConfig = allConfigs.firstWhere(
      (c) => c.protocol == 'WireGuard',
      orElse: () => config,
    );
    
    if (wireGuardConfig.protocol == 'WireGuard') {
      print('\nWireGuard配置文件内容预览:');
      print('─' * 40);
      final wgContent = wireGuardConfig.generateWireGuardConfig();
      final wgLines = wgContent.split('\n');
      for (int i = 0; i < wgLines.length && i < 8; i++) {
        print('${(i + 1).toString().padLeft(2)}: ${wgLines[i]}');
      }
    }
  }

  print('\n🎉 演示完成!');
  print('=' * 50);
  print('💡 提示: 在实际应用中，您可以通过UI界面使用这些功能');
  print('   - 客户端生成器页面: 生成新的客户端配置');
  print('   - 配置管理页面: 管理现有配置');
  print('   - 主页面: 连接和使用VPN');
}

/// 辅助函数：格式化JSON输出
String formatJson(Map<String, dynamic> json) {
  const encoder = JsonEncoder.withIndent('  ');
  return encoder.convert(json);
}
