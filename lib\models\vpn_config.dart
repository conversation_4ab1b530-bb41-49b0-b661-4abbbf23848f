import 'dart:convert';

/// VPN配置模型类
class VpnConfig {
  final String id;
  final String name;
  final String serverAddress;
  final int port;
  final String protocol; // IKEv2, OpenVPN, WireGuard
  final String username;
  final String password;
  final String? certificate;
  final String? privateKey;
  final String? caCertificate;
  final Map<String, dynamic>? additionalSettings;
  final DateTime createdAt;
  final DateTime? lastConnected;
  final bool isActive;
  final String country;
  final String flagAsset;

  VpnConfig({
    required this.id,
    required this.name,
    required this.serverAddress,
    required this.port,
    required this.protocol,
    required this.username,
    required this.password,
    this.certificate,
    this.privateKey,
    this.caCertificate,
    this.additionalSettings,
    required this.createdAt,
    this.lastConnected,
    this.isActive = true,
    required this.country,
    required this.flagAsset,
  });

  /// 从JSON创建VpnConfig对象
  factory VpnConfig.fromJson(Map<String, dynamic> json) {
    return VpnConfig(
      id: json['id'],
      name: json['name'],
      serverAddress: json['serverAddress'],
      port: json['port'],
      protocol: json['protocol'],
      username: json['username'],
      password: json['password'],
      certificate: json['certificate'],
      privateKey: json['privateKey'],
      caCertificate: json['caCertificate'],
      additionalSettings: json['additionalSettings'],
      createdAt: DateTime.parse(json['createdAt']),
      lastConnected: json['lastConnected'] != null 
          ? DateTime.parse(json['lastConnected']) 
          : null,
      isActive: json['isActive'] ?? true,
      country: json['country'],
      flagAsset: json['flagAsset'],
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'serverAddress': serverAddress,
      'port': port,
      'protocol': protocol,
      'username': username,
      'password': password,
      'certificate': certificate,
      'privateKey': privateKey,
      'caCertificate': caCertificate,
      'additionalSettings': additionalSettings,
      'createdAt': createdAt.toIso8601String(),
      'lastConnected': lastConnected?.toIso8601String(),
      'isActive': isActive,
      'country': country,
      'flagAsset': flagAsset,
    };
  }

  /// 创建副本
  VpnConfig copyWith({
    String? id,
    String? name,
    String? serverAddress,
    int? port,
    String? protocol,
    String? username,
    String? password,
    String? certificate,
    String? privateKey,
    String? caCertificate,
    Map<String, dynamic>? additionalSettings,
    DateTime? createdAt,
    DateTime? lastConnected,
    bool? isActive,
    String? country,
    String? flagAsset,
  }) {
    return VpnConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      serverAddress: serverAddress ?? this.serverAddress,
      port: port ?? this.port,
      protocol: protocol ?? this.protocol,
      username: username ?? this.username,
      password: password ?? this.password,
      certificate: certificate ?? this.certificate,
      privateKey: privateKey ?? this.privateKey,
      caCertificate: caCertificate ?? this.caCertificate,
      additionalSettings: additionalSettings ?? this.additionalSettings,
      createdAt: createdAt ?? this.createdAt,
      lastConnected: lastConnected ?? this.lastConnected,
      isActive: isActive ?? this.isActive,
      country: country ?? this.country,
      flagAsset: flagAsset ?? this.flagAsset,
    );
  }

  /// 生成OpenVPN配置文件内容
  String generateOpenVpnConfig() {
    final buffer = StringBuffer();
    buffer.writeln('client');
    buffer.writeln('dev tun');
    buffer.writeln('proto udp');
    buffer.writeln('remote $serverAddress $port');
    buffer.writeln('resolv-retry infinite');
    buffer.writeln('nobind');
    buffer.writeln('persist-key');
    buffer.writeln('persist-tun');
    buffer.writeln('auth-user-pass');
    
    if (caCertificate != null) {
      buffer.writeln('<ca>');
      buffer.writeln(caCertificate);
      buffer.writeln('</ca>');
    }
    
    if (certificate != null) {
      buffer.writeln('<cert>');
      buffer.writeln(certificate);
      buffer.writeln('</cert>');
    }
    
    if (privateKey != null) {
      buffer.writeln('<key>');
      buffer.writeln(privateKey);
      buffer.writeln('</key>');
    }
    
    buffer.writeln('comp-lzo');
    buffer.writeln('verb 3');
    
    return buffer.toString();
  }

  /// 生成WireGuard配置文件内容
  String generateWireGuardConfig() {
    final buffer = StringBuffer();
    buffer.writeln('[Interface]');
    if (privateKey != null) {
      buffer.writeln('PrivateKey = $privateKey');
    }
    buffer.writeln('Address = ********/24');
    buffer.writeln('DNS = *******');
    buffer.writeln('');
    buffer.writeln('[Peer]');
    if (certificate != null) {
      buffer.writeln('PublicKey = $certificate');
    }
    buffer.writeln('Endpoint = $serverAddress:$port');
    buffer.writeln('AllowedIPs = 0.0.0.0/0');
    buffer.writeln('PersistentKeepalive = 25');
    
    return buffer.toString();
  }

  @override
  String toString() {
    return 'VpnConfig(id: $id, name: $name, serverAddress: $serverAddress, protocol: $protocol)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is VpnConfig && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
