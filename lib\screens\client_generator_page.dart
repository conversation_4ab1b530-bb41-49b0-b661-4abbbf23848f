import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/vpn_config.dart';
import '../services/vpn_client_generator.dart';
import '../services/vpn_config_service.dart';

/// 客户端生成器页面
class ClientGeneratorPage extends StatefulWidget {
  @override
  _ClientGeneratorPageState createState() => _ClientGeneratorPageState();
}

class _ClientGeneratorPageState extends State<ClientGeneratorPage> {
  final _formKey = GlobalKey<FormState>();
  final _clientNameController = TextEditingController();
  final _serverAddressController = TextEditingController();
  final _portController = TextEditingController(text: '1194');
  final _dnsController = TextEditingController(text: '*******,*******');
  
  String _selectedProtocol = 'OpenVPN';
  bool _enableKillSwitch = true;
  bool _enableAutoReconnect = true;
  bool _isGenerating = false;
  
  final List<String> _protocols = ['OpenVPN', 'IKEv2', 'WireGuard'];
  final VpnClientGenerator _generator = VpnClientGenerator.instance;
  final VpnConfigService _configService = VpnConfigService.instance;

  @override
  void dispose() {
    _clientNameController.dispose();
    _serverAddressController.dispose();
    _portController.dispose();
    _dnsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('VPN客户端生成器'),
        backgroundColor: Theme.of(context).primaryColor,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildHeaderCard(),
              SizedBox(height: 16),
              _buildConfigurationCard(),
              SizedBox(height: 16),
              _buildAdvancedSettingsCard(),
              SizedBox(height: 24),
              _buildGenerateButton(),
              SizedBox(height: 16),
              _buildBatchGeneratorCard(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.vpn_key, color: Theme.of(context).primaryColor),
                SizedBox(width: 8),
                Text(
                  'VPN客户端配置生成器',
                  style: Theme.of(context).textTheme.titleLarge,
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              '为您的VPN服务器生成客户端配置文件，支持多种协议和平台。',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildConfigurationCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '基本配置',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _clientNameController,
              decoration: InputDecoration(
                labelText: '客户端名称',
                hintText: '例如：John的手机',
                prefixIcon: Icon(Icons.person),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入客户端名称';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _serverAddressController,
              decoration: InputDecoration(
                labelText: 'VPN服务器地址',
                hintText: '例如：vpn.example.com 或 *************',
                prefixIcon: Icon(Icons.dns),
                border: OutlineInputBorder(),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return '请输入服务器地址';
                }
                return null;
              },
            ),
            SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  flex: 2,
                  child: DropdownButtonFormField<String>(
                    value: _selectedProtocol,
                    decoration: InputDecoration(
                      labelText: '协议类型',
                      prefixIcon: Icon(Icons.security),
                      border: OutlineInputBorder(),
                    ),
                    items: _protocols.map((protocol) {
                      return DropdownMenuItem(
                        value: protocol,
                        child: Text(protocol),
                      );
                    }).toList(),
                    onChanged: (value) {
                      setState(() {
                        _selectedProtocol = value!;
                        // 根据协议设置默认端口
                        switch (value) {
                          case 'OpenVPN':
                            _portController.text = '1194';
                            break;
                          case 'IKEv2':
                            _portController.text = '500';
                            break;
                          case 'WireGuard':
                            _portController.text = '51820';
                            break;
                        }
                      });
                    },
                  ),
                ),
                SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    controller: _portController,
                    decoration: InputDecoration(
                      labelText: '端口',
                      prefixIcon: Icon(Icons.router),
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return '请输入端口';
                      }
                      final port = int.tryParse(value);
                      if (port == null || port < 1 || port > 65535) {
                        return '端口范围1-65535';
                      }
                      return null;
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAdvancedSettingsCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '高级设置',
              style: Theme.of(context).textTheme.titleMedium,
            ),
            SizedBox(height: 16),
            TextFormField(
              controller: _dnsController,
              decoration: InputDecoration(
                labelText: 'DNS服务器',
                hintText: '多个DNS用逗号分隔',
                prefixIcon: Icon(Icons.dns),
                border: OutlineInputBorder(),
              ),
            ),
            SizedBox(height: 16),
            SwitchListTile(
              title: Text('启用Kill Switch'),
              subtitle: Text('VPN断开时自动断开网络连接'),
              value: _enableKillSwitch,
              onChanged: (value) {
                setState(() {
                  _enableKillSwitch = value;
                });
              },
            ),
            SwitchListTile(
              title: Text('启用自动重连'),
              subtitle: Text('连接断开时自动尝试重新连接'),
              value: _enableAutoReconnect,
              onChanged: (value) {
                setState(() {
                  _enableAutoReconnect = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildGenerateButton() {
    return ElevatedButton.icon(
      onPressed: _isGenerating ? null : _generateClient,
      icon: _isGenerating 
          ? SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(strokeWidth: 2),
            )
          : Icon(Icons.build),
      label: Text(_isGenerating ? '正在生成...' : '生成客户端配置'),
      style: ElevatedButton.styleFrom(
        padding: EdgeInsets.symmetric(vertical: 16),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
    );
  }

  Widget _buildBatchGeneratorCard() {
    return Card(
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.group, color: Theme.of(context).primaryColor),
                SizedBox(width: 8),
                Text(
                  '批量生成',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
            SizedBox(height: 8),
            Text(
              '一次性为多个客户端生成配置文件',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _showBatchGeneratorDialog,
              icon: Icon(Icons.group_add),
              label: Text('批量生成客户端'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _generateClient() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isGenerating = true;
    });

    try {
      final result = await _generator.generateClientPackage(
        clientName: _clientNameController.text.trim(),
        serverAddress: _serverAddressController.text.trim(),
        port: int.parse(_portController.text),
        protocol: _selectedProtocol,
        customDns: _dnsController.text.trim(),
        enableKillSwitch: _enableKillSwitch,
        enableAutoReconnect: _enableAutoReconnect,
      );

      if (result['success']) {
        // 保存配置到本地
        final vpnConfig = VpnConfig.fromJson(result['vpnConfig']);
        await _configService.saveConfig(vpnConfig);

        _showSuccessDialog(result);
      } else {
        _showErrorDialog(result['error']);
      }
    } catch (e) {
      _showErrorDialog('生成客户端配置失败: $e');
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  void _showSuccessDialog(Map<String, dynamic> result) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.green),
            SizedBox(width: 8),
            Text('生成成功'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('客户端配置已成功生成！'),
            SizedBox(height: 16),
            Text('客户端信息:', style: TextStyle(fontWeight: FontWeight.bold)),
            Text('名称: ${result['clientInfo']['clientName']}'),
            Text('ID: ${result['clientInfo']['clientId']}'),
            Text('协议: ${result['clientInfo']['protocol']}'),
            Text('服务器: ${result['clientInfo']['serverAddress']}:${result['clientInfo']['port']}'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('确定'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _showConfigFilesDialog(result['configFiles']);
            },
            child: Text('查看配置文件'),
          ),
        ],
      ),
    );
  }

  void _showErrorDialog(String error) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.error, color: Colors.red),
            SizedBox(width: 8),
            Text('生成失败'),
          ],
        ),
        content: Text(error),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showConfigFilesDialog(Map<String, String> configFiles) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('配置文件'),
        content: Container(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: configFiles.length,
            itemBuilder: (context, index) {
              final fileName = configFiles.keys.elementAt(index);
              final content = configFiles[fileName]!;
              
              return Card(
                child: ExpansionTile(
                  title: Text(fileName),
                  children: [
                    Padding(
                      padding: EdgeInsets.all(16),
                      child: Container(
                        width: double.infinity,
                        padding: EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.grey[100],
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          content,
                          style: TextStyle(
                            fontFamily: 'monospace',
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }

  void _showBatchGeneratorDialog() {
    final clientNamesController = TextEditingController();
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('批量生成客户端'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('请输入客户端名称，每行一个：'),
            SizedBox(height: 16),
            TextField(
              controller: clientNamesController,
              maxLines: 5,
              decoration: InputDecoration(
                hintText: '客户端1\n客户端2\n客户端3',
                border: OutlineInputBorder(),
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('取消'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _generateBatchClients(clientNamesController.text);
            },
            child: Text('生成'),
          ),
        ],
      ),
    );
  }

  Future<void> _generateBatchClients(String clientNamesText) async {
    if (!_formKey.currentState!.validate()) return;

    final clientNames = clientNamesText
        .split('\n')
        .map((name) => name.trim())
        .where((name) => name.isNotEmpty)
        .toList();

    if (clientNames.isEmpty) {
      _showErrorDialog('请输入至少一个客户端名称');
      return;
    }

    setState(() {
      _isGenerating = true;
    });

    try {
      final results = await _generator.generateBatchClients(
        clientNames: clientNames,
        serverAddress: _serverAddressController.text.trim(),
        port: int.parse(_portController.text),
        protocol: _selectedProtocol,
        customDns: _dnsController.text.trim(),
        enableKillSwitch: _enableKillSwitch,
        enableAutoReconnect: _enableAutoReconnect,
      );

      // 保存所有成功生成的配置
      for (final result in results) {
        if (result['success']) {
          final vpnConfig = VpnConfig.fromJson(result['vpnConfig']);
          await _configService.saveConfig(vpnConfig);
        }
      }

      _showBatchResultDialog(results);
    } catch (e) {
      _showErrorDialog('批量生成失败: $e');
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  void _showBatchResultDialog(List<Map<String, dynamic>> results) {
    final successCount = results.where((r) => r['success']).length;
    final failureCount = results.length - successCount;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('批量生成结果'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('总计: ${results.length} 个客户端'),
            Text('成功: $successCount 个', style: TextStyle(color: Colors.green)),
            if (failureCount > 0)
              Text('失败: $failureCount 个', style: TextStyle(color: Colors.red)),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('确定'),
          ),
        ],
      ),
    );
  }
}
