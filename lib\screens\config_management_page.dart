import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/vpn_config.dart';
import '../services/vpn_config_service.dart';
import '../services/vpn_client_generator.dart';

/// 配置管理页面
class ConfigManagementPage extends StatefulWidget {
  @override
  _ConfigManagementPageState createState() => _ConfigManagementPageState();
}

class _ConfigManagementPageState extends State<ConfigManagementPage> {
  final VpnConfigService _configService = VpnConfigService.instance;
  final VpnClientGenerator _generator = VpnClientGenerator.instance;
  
  List<VpnConfig> _configs = [];
  VpnConfig? _activeConfig;
  bool _isLoading = true;
  String _searchQuery = '';

  @override
  void initState() {
    super.initState();
    _loadConfigs();
  }

  Future<void> _loadConfigs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final configs = await _configService.getAllConfigs();
      final activeConfig = await _configService.getActiveConfig();
      
      setState(() {
        _configs = configs;
        _activeConfig = activeConfig;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('加载配置失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('配置管理'),
        backgroundColor: Theme.of(context).primaryColor,
        actions: [
          IconButton(
            icon: Icon(Icons.add),
            onPressed: _showAddConfigDialog,
          ),
          PopupMenuButton<String>(
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'import',
                child: Row(
                  children: [
                    Icon(Icons.file_upload),
                    SizedBox(width: 8),
                    Text('导入配置'),
                  ],
                ),
              ),
              PopupMenuItem(
                value: 'export_all',
                child: Row(
                  children: [
                    Icon(Icons.file_download),
                    SizedBox(width: 8),
                    Text('导出所有配置'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          _buildSearchBar(),
          _buildStatsCard(),
          Expanded(
            child: _isLoading
                ? Center(child: CircularProgressIndicator())
                : _buildConfigList(),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Padding(
      padding: EdgeInsets.all(16),
      child: TextField(
        decoration: InputDecoration(
          hintText: '搜索配置...',
          prefixIcon: Icon(Icons.search),
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
        onChanged: (value) {
          setState(() {
            _searchQuery = value.toLowerCase();
          });
        },
      ),
    );
  }

  Widget _buildStatsCard() {
    final totalConfigs = _configs.length;
    final activeConfigs = _configs.where((c) => c.isActive).length;
    final protocols = _configs.map((c) => c.protocol).toSet().toList();

    return Card(
      margin: EdgeInsets.symmetric(horizontal: 16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('总配置', totalConfigs.toString(), Icons.vpn_key),
            _buildStatItem('活跃配置', activeConfigs.toString(), Icons.check_circle),
            _buildStatItem('协议类型', protocols.length.toString(), Icons.security),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(icon, color: Theme.of(context).primaryColor),
        SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall,
        ),
      ],
    );
  }

  Widget _buildConfigList() {
    final filteredConfigs = _configs.where((config) {
      return config.name.toLowerCase().contains(_searchQuery) ||
             config.serverAddress.toLowerCase().contains(_searchQuery) ||
             config.protocol.toLowerCase().contains(_searchQuery);
    }).toList();

    if (filteredConfigs.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.vpn_key_off,
              size: 64,
              color: Colors.grey,
            ),
            SizedBox(height: 16),
            Text(
              _searchQuery.isEmpty ? '暂无配置' : '未找到匹配的配置',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Colors.grey,
              ),
            ),
            if (_searchQuery.isEmpty) ...[
              SizedBox(height: 8),
              ElevatedButton.icon(
                onPressed: _showAddConfigDialog,
                icon: Icon(Icons.add),
                label: Text('添加配置'),
              ),
            ],
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(16),
      itemCount: filteredConfigs.length,
      itemBuilder: (context, index) {
        final config = filteredConfigs[index];
        return _buildConfigCard(config);
      },
    );
  }

  Widget _buildConfigCard(VpnConfig config) {
    final isActive = _activeConfig?.id == config.id;
    
    return Card(
      margin: EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: () => _showConfigDetails(config),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundColor: isActive ? Colors.green : Colors.grey,
                    child: Icon(
                      isActive ? Icons.check : Icons.vpn_key,
                      color: Colors.white,
                    ),
                  ),
                  SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          config.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${config.serverAddress}:${config.port}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Chip(
                    label: Text(config.protocol),
                    backgroundColor: _getProtocolColor(config.protocol),
                    labelStyle: TextStyle(color: Colors.white, fontSize: 12),
                  ),
                  PopupMenuButton<String>(
                    onSelected: (action) => _handleConfigAction(action, config),
                    itemBuilder: (context) => [
                      if (!isActive)
                        PopupMenuItem(
                          value: 'activate',
                          child: Row(
                            children: [
                              Icon(Icons.play_arrow),
                              SizedBox(width: 8),
                              Text('设为活跃'),
                            ],
                          ),
                        ),
                      if (isActive)
                        PopupMenuItem(
                          value: 'deactivate',
                          child: Row(
                            children: [
                              Icon(Icons.stop),
                              SizedBox(width: 8),
                              Text('取消活跃'),
                            ],
                          ),
                        ),
                      PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('编辑'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'export',
                        child: Row(
                          children: [
                            Icon(Icons.file_download),
                            SizedBox(width: 8),
                            Text('导出'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'qr',
                        child: Row(
                          children: [
                            Icon(Icons.qr_code),
                            SizedBox(width: 8),
                            Text('生成QR码'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: Colors.red),
                            SizedBox(width: 8),
                            Text('删除', style: TextStyle(color: Colors.red)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              if (config.lastConnected != null) ...[
                SizedBox(height: 8),
                Text(
                  '最后连接: ${_formatDateTime(config.lastConnected!)}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Color _getProtocolColor(String protocol) {
    switch (protocol) {
      case 'OpenVPN':
        return Colors.blue;
      case 'IKEv2':
        return Colors.green;
      case 'WireGuard':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.year}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.day.toString().padLeft(2, '0')} '
           '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  void _handleMenuAction(String action) {
    switch (action) {
      case 'import':
        _showImportDialog();
        break;
      case 'export_all':
        _exportAllConfigs();
        break;
    }
  }

  void _handleConfigAction(String action, VpnConfig config) {
    switch (action) {
      case 'activate':
        _setActiveConfig(config);
        break;
      case 'deactivate':
        _clearActiveConfig();
        break;
      case 'edit':
        _showEditConfigDialog(config);
        break;
      case 'export':
        _exportConfig(config);
        break;
      case 'qr':
        _generateQrCode(config);
        break;
      case 'delete':
        _deleteConfig(config);
        break;
    }
  }

  Future<void> _setActiveConfig(VpnConfig config) async {
    final success = await _configService.setActiveConfig(config.id);
    if (success) {
      setState(() {
        _activeConfig = config;
      });
      _showSuccessSnackBar('已设置为活跃配置');
    } else {
      _showErrorSnackBar('设置活跃配置失败');
    }
  }

  Future<void> _clearActiveConfig() async {
    final success = await _configService.clearActiveConfig();
    if (success) {
      setState(() {
        _activeConfig = null;
      });
      _showSuccessSnackBar('已取消活跃配置');
    } else {
      _showErrorSnackBar('取消活跃配置失败');
    }
  }

  Future<void> _deleteConfig(VpnConfig config) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('确认删除'),
        content: Text('确定要删除配置 "${config.name}" 吗？此操作不可撤销。'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: Text('删除'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _configService.deleteConfig(config.id);
      if (success) {
        _loadConfigs();
        _showSuccessSnackBar('配置已删除');
      } else {
        _showErrorSnackBar('删除配置失败');
      }
    }
  }

  void _showConfigDetails(VpnConfig config) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(config.name),
        content: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildDetailRow('服务器地址', config.serverAddress),
              _buildDetailRow('端口', config.port.toString()),
              _buildDetailRow('协议', config.protocol),
              _buildDetailRow('用户名', config.username),
              _buildDetailRow('国家', config.country),
              _buildDetailRow('创建时间', _formatDateTime(config.createdAt)),
              if (config.lastConnected != null)
                _buildDetailRow('最后连接', _formatDateTime(config.lastConnected!)),
              _buildDetailRow('状态', config.isActive ? '活跃' : '非活跃'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('关闭'),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(
            child: Text(value),
          ),
        ],
      ),
    );
  }

  void _showAddConfigDialog() {
    // 这里可以导航到客户端生成器页面或显示添加配置对话框
    Navigator.pushNamed(context, '/client-generator');
  }

  void _showEditConfigDialog(VpnConfig config) {
    // 实现编辑配置对话框
    _showSuccessSnackBar('编辑功能开发中...');
  }

  void _showImportDialog() {
    // 实现导入配置对话框
    _showSuccessSnackBar('导入功能开发中...');
  }

  Future<void> _exportConfig(VpnConfig config) async {
    try {
      final filePath = await _generator.exportConfig(config, config.protocol);
      if (filePath != null) {
        _showSuccessSnackBar('配置已导出到: $filePath');
      } else {
        _showErrorSnackBar('导出配置失败');
      }
    } catch (e) {
      _showErrorSnackBar('导出配置失败: $e');
    }
  }

  Future<void> _exportAllConfigs() async {
    try {
      int successCount = 0;
      for (final config in _configs) {
        final filePath = await _generator.exportConfig(config, config.protocol);
        if (filePath != null) {
          successCount++;
        }
      }
      _showSuccessSnackBar('成功导出 $successCount 个配置');
    } catch (e) {
      _showErrorSnackBar('批量导出失败: $e');
    }
  }

  Future<void> _generateQrCode(VpnConfig config) async {
    try {
      final qrData = await _generator.generateQrConfig(config);
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text('QR码配置'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text('扫描此QR码以导入配置:'),
              SizedBox(height: 16),
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.grey[100],
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  qrData,
                  style: TextStyle(fontFamily: 'monospace', fontSize: 10),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () {
                Clipboard.setData(ClipboardData(text: qrData));
                _showSuccessSnackBar('QR码数据已复制到剪贴板');
              },
              child: Text('复制'),
            ),
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text('关闭'),
            ),
          ],
        ),
      );
    } catch (e) {
      _showErrorSnackBar('生成QR码失败: $e');
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }
}
