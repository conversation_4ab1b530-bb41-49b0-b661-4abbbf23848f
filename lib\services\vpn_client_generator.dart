import 'dart:convert';
import 'dart:io';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/vpn_config.dart';

/// VPN客户端生成器
class VpnClientGenerator {
  static VpnClientGenerator? _instance;
  
  VpnClientGenerator._();
  
  static VpnClientGenerator get instance {
    _instance ??= VpnClientGenerator._();
    return _instance!;
  }

  /// 生成完整的VPN客户端配置包
  Future<Map<String, dynamic>> generateClientPackage({
    required String clientName,
    required String serverAddress,
    required int port,
    required String protocol,
    String? customDns,
    bool enableKillSwitch = true,
    bool enableAutoReconnect = true,
    Map<String, dynamic>? advancedSettings,
  }) async {
    try {
      final clientId = const Uuid().v4();
      final timestamp = DateTime.now();
      
      // 生成客户端证书和密钥
      final certificates = await _generateCertificates(clientName, clientId);
      
      // 创建VPN配置
      final vpnConfig = VpnConfig(
        id: clientId,
        name: clientName,
        serverAddress: serverAddress,
        port: port,
        protocol: protocol,
        username: _generateUsername(clientName),
        password: _generateSecurePassword(),
        certificate: certificates['clientCert'],
        privateKey: certificates['clientKey'],
        caCertificate: certificates['caCert'],
        additionalSettings: {
          'dns': customDns ?? '*******,*******',
          'killSwitch': enableKillSwitch,
          'autoReconnect': enableAutoReconnect,
          'mtu': 1500,
          'keepAlive': 25,
          ...?advancedSettings,
        },
        createdAt: timestamp,
        country: 'Custom',
        flagAsset: 'assets/logo.png',
      );

      // 生成配置文件
      final configFiles = await _generateConfigFiles(vpnConfig);
      
      // 生成安装脚本
      final installScripts = await _generateInstallScripts(vpnConfig);
      
      // 生成客户端信息
      final clientInfo = {
        'clientId': clientId,
        'clientName': clientName,
        'serverAddress': serverAddress,
        'port': port,
        'protocol': protocol,
        'createdAt': timestamp.toIso8601String(),
        'expiresAt': timestamp.add(const Duration(days: 365)).toIso8601String(),
        'status': 'active',
        'downloadCount': 0,
        'lastDownload': null,
      };

      return {
        'success': true,
        'clientInfo': clientInfo,
        'vpnConfig': vpnConfig.toJson(),
        'configFiles': configFiles,
        'installScripts': installScripts,
        'certificates': certificates,
      };
    } catch (e) {
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// 生成批量客户端配置
  Future<List<Map<String, dynamic>>> generateBatchClients({
    required List<String> clientNames,
    required String serverAddress,
    required int port,
    required String protocol,
    String? customDns,
    bool enableKillSwitch = true,
    bool enableAutoReconnect = true,
  }) async {
    final results = <Map<String, dynamic>>[];
    
    for (final clientName in clientNames) {
      final result = await generateClientPackage(
        clientName: clientName,
        serverAddress: serverAddress,
        port: port,
        protocol: protocol,
        customDns: customDns,
        enableKillSwitch: enableKillSwitch,
        enableAutoReconnect: enableAutoReconnect,
      );
      results.add(result);
    }
    
    return results;
  }

  /// 生成QR码配置（用于移动设备）
  Future<String> generateQrConfig(VpnConfig config) async {
    try {
      final qrData = {
        'type': 'vpn_config',
        'version': '1.0',
        'config': {
          'name': config.name,
          'server': config.serverAddress,
          'port': config.port,
          'protocol': config.protocol,
          'username': config.username,
          'password': config.password,
        },
      };
      
      return base64Encode(utf8.encode(jsonEncode(qrData)));
    } catch (e) {
      throw Exception('生成QR码配置失败: $e');
    }
  }

  /// 验证客户端配置
  Future<Map<String, dynamic>> validateClientConfig(VpnConfig config) async {
    final issues = <String>[];
    final warnings = <String>[];
    
    // 检查服务器地址
    if (config.serverAddress.isEmpty) {
      issues.add('服务器地址不能为空');
    } else if (!_isValidServerAddress(config.serverAddress)) {
      warnings.add('服务器地址格式可能不正确');
    }
    
    // 检查端口
    if (config.port < 1 || config.port > 65535) {
      issues.add('端口号必须在1-65535之间');
    }
    
    // 检查协议
    if (!['IKEv2', 'OpenVPN', 'WireGuard'].contains(config.protocol)) {
      issues.add('不支持的协议类型: ${config.protocol}');
    }
    
    // 检查认证信息
    if (config.username.isEmpty && config.certificate == null) {
      issues.add('必须提供用户名或证书');
    }
    
    if (config.username.isNotEmpty && config.password.isEmpty) {
      warnings.add('建议设置密码');
    }
    
    return {
      'isValid': issues.isEmpty,
      'issues': issues,
      'warnings': warnings,
    };
  }

  /// 生成证书和密钥
  Future<Map<String, String>> _generateCertificates(String clientName, String clientId) async {
    // 这里是简化的证书生成，实际应用中应该使用真实的CA
    final random = Random.secure();
    
    // 生成CA证书
    final caCert = _generateMockCertificate('VPN-CA', 'CA', DateTime.now().add(const Duration(days: 3650)));
    
    // 生成客户端证书
    final clientCert = _generateMockCertificate(clientName, clientId, DateTime.now().add(const Duration(days: 365)));
    
    // 生成私钥
    final privateKey = _generateMockPrivateKey();
    
    return {
      'caCert': caCert,
      'clientCert': clientCert,
      'clientKey': privateKey,
    };
  }

  /// 生成配置文件
  Future<Map<String, String>> _generateConfigFiles(VpnConfig config) async {
    final files = <String, String>{};
    
    // OpenVPN配置文件
    if (config.protocol == 'OpenVPN') {
      files['client.ovpn'] = config.generateOpenVpnConfig();
    }
    
    // WireGuard配置文件
    if (config.protocol == 'WireGuard') {
      files['client.conf'] = config.generateWireGuardConfig();
    }
    
    // 通用JSON配置
    files['config.json'] = jsonEncode(config.toJson());
    
    // 连接脚本
    files['connect.sh'] = _generateConnectScript(config);
    files['disconnect.sh'] = _generateDisconnectScript(config);
    
    return files;
  }

  /// 生成安装脚本
  Future<Map<String, String>> _generateInstallScripts(VpnConfig config) async {
    final scripts = <String, String>{};
    
    // Linux安装脚本
    scripts['install_linux.sh'] = '''#!/bin/bash
# VPN客户端安装脚本 - Linux
echo "安装VPN客户端配置..."

# 检查OpenVPN是否已安装
if ! command -v openvpn &> /dev/null; then
    echo "正在安装OpenVPN..."
    sudo apt-get update
    sudo apt-get install -y openvpn
fi

# 复制配置文件
sudo cp client.ovpn /etc/openvpn/
sudo cp config.json /etc/openvpn/

echo "安装完成！"
echo "使用以下命令连接VPN:"
echo "sudo openvpn --config /etc/openvpn/client.ovpn"
''';

    // Windows安装脚本
    scripts['install_windows.bat'] = '''@echo off
REM VPN客户端安装脚本 - Windows
echo 安装VPN客户端配置...

REM 检查OpenVPN是否已安装
where openvpn >nul 2>nul
if %errorlevel% neq 0 (
    echo 请先安装OpenVPN客户端
    echo 下载地址: https://openvpn.net/community-downloads/
    pause
    exit /b 1
)

REM 复制配置文件到OpenVPN配置目录
copy client.ovpn "%PROGRAMFILES%\\OpenVPN\\config\\"
copy config.json "%PROGRAMFILES%\\OpenVPN\\config\\"

echo 安装完成！
echo 请使用OpenVPN GUI连接VPN
pause
''';

    // macOS安装脚本
    scripts['install_macos.sh'] = '''#!/bin/bash
# VPN客户端安装脚本 - macOS
echo "安装VPN客户端配置..."

# 检查Homebrew是否已安装
if ! command -v brew &> /dev/null; then
    echo "请先安装Homebrew: https://brew.sh/"
    exit 1
fi

# 安装OpenVPN
if ! command -v openvpn &> /dev/null; then
    echo "正在安装OpenVPN..."
    brew install openvpn
fi

# 创建配置目录
mkdir -p ~/vpn-configs

# 复制配置文件
cp client.ovpn ~/vpn-configs/
cp config.json ~/vpn-configs/

echo "安装完成！"
echo "使用以下命令连接VPN:"
echo "sudo openvpn --config ~/vpn-configs/client.ovpn"
''';

    return scripts;
  }

  /// 生成用户名
  String _generateUsername(String clientName) {
    final sanitized = clientName.toLowerCase().replaceAll(RegExp(r'[^a-z0-9]'), '');
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString().substring(8);
    return '${sanitized}_$timestamp';
  }

  /// 生成安全密码
  String _generateSecurePassword({int length = 16}) {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#\$%^&*';
    final random = Random.secure();
    return String.fromCharCodes(
      Iterable.generate(length, (_) => chars.codeUnitAt(random.nextInt(chars.length))),
    );
  }

  /// 生成模拟证书
  String _generateMockCertificate(String name, String id, DateTime expiry) {
    return '''-----BEGIN CERTIFICATE-----
MIIDXTCCAkWgAwIBAgIJAKoK/heBjcOuMA0GCSqGSIb3DQEBCwUAMEUxCzAJBgNV
BAYTAkFVMRMwEQYDVQQIDApTb21lLVN0YXRlMSEwHwYDVQQKDBhJbnRlcm5ldCBX
aWRnaXRzIFB0eSBMdGQwHhcNMjMwMTAxMDAwMDAwWhcNMjQwMTAxMDAwMDAwWjBF
MQswCQYDVQQGEwJBVTETMBEGA1UECAwKU29tZS1TdGF0ZTEhMB8GA1UECgwYSW50
ZXJuZXQgV2lkZ2l0cyBQdHkgTHRkMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIB
CgKCAQEAuuWiNVJKvdvpDQIDAQABo1MwUTAdBgNVHQ4EFgQUhKtzpL+hlGkHqaFc
VxjRR4TAY1AwHwYDVR0jBBgwFoAUhKtzpL+hlGkHqaFcVxjRR4TAY1AwDwYDVR0T
AQH/BAUwAwEB/zANBgkqhkiG9w0BAQsFAAOCAQEAZvBQON9PvNiYR+IJidoHBHBy
1L27aB5QlTyrhXPFBa2muDc=
-----END CERTIFICATE-----''';
  }

  /// 生成模拟私钥
  String _generateMockPrivateKey() {
    return '''-----BEGIN PRIVATE KEY-----
MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC65aI1Ukq92+kN
AgMBAAECggEBALrloTVSSr3b6Q0CAwEAAQKCAQEAuuWiNVJKvdvpDQIDAQABo1Mw
UTAdBgNVHQ4EFgQUhKtzpL+hlGkHqaFcVxjRR4TAY1AwHwYDVR0jBBgwFoAUhKtz
pL+hlGkHqaFcVxjRR4TAY1AwDwYDVR0TAQH/BAUwAwEB/zANBgkqhkiG9w0BAQsF
AAOCAQEAZvBQON9PvNiYR+IJidoHBHBy1L27aB5QlTyrhXPFBa2muDc=
-----END PRIVATE KEY-----''';
  }

  /// 生成连接脚本
  String _generateConnectScript(VpnConfig config) {
    return '''#!/bin/bash
# VPN连接脚本
echo "正在连接到VPN服务器: ${config.serverAddress}"
openvpn --config client.ovpn --daemon
echo "VPN连接已启动"
''';
  }

  /// 生成断开连接脚本
  String _generateDisconnectScript(VpnConfig config) {
    return '''#!/bin/bash
# VPN断开连接脚本
echo "正在断开VPN连接..."
sudo killall openvpn
echo "VPN连接已断开"
''';
  }

  /// 导出配置文件
  Future<String?> exportConfig(VpnConfig config, String format) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${config.name}_${format.toLowerCase()}.${_getFileExtension(format)}';
      final file = File('${directory.path}/$fileName');

      String content;
      switch (format.toUpperCase()) {
        case 'OPENVPN':
          content = config.generateOpenVpnConfig();
          break;
        case 'WIREGUARD':
          content = config.generateWireGuardConfig();
          break;
        case 'JSON':
          content = jsonEncode(config.toJson());
          break;
        default:
          throw Exception('不支持的格式: $format');
      }

      await file.writeAsString(content);
      return file.path;
    } catch (e) {
      print('导出配置失败: $e');
      return null;
    }
  }

  /// 获取文件扩展名
  String _getFileExtension(String format) {
    switch (format.toUpperCase()) {
      case 'OPENVPN':
        return 'ovpn';
      case 'WIREGUARD':
        return 'conf';
      case 'JSON':
        return 'json';
      default:
        return 'txt';
    }
  }

  /// 验证服务器地址格式
  bool _isValidServerAddress(String address) {
    // 简单的IP地址或域名验证
    final ipRegex = RegExp(r'^(\d{1,3}\.){3}\d{1,3}$');
    final domainRegex = RegExp(r'^[a-zA-Z0-9][a-zA-Z0-9-]{1,61}[a-zA-Z0-9]\.[a-zA-Z]{2,}$');

    return ipRegex.hasMatch(address) || domainRegex.hasMatch(address);
  }
}
