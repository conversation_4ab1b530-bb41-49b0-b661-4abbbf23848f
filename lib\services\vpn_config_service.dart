import 'dart:convert';
import 'dart:io';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:path_provider/path_provider.dart';
import 'package:uuid/uuid.dart';
import '../models/vpn_config.dart';

/// VPN配置管理服务
class VpnConfigService {
  static const String _configsKey = 'vpn_configs';
  static const String _activeConfigKey = 'active_vpn_config';
  static VpnConfigService? _instance;
  
  VpnConfigService._();
  
  static VpnConfigService get instance {
    _instance ??= VpnConfigService._();
    return _instance!;
  }

  /// 获取所有VPN配置
  Future<List<VpnConfig>> getAllConfigs() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configsJson = prefs.getStringList(_configsKey) ?? [];
      
      return configsJson.map((configJson) {
        final Map<String, dynamic> json = jsonDecode(configJson);
        return VpnConfig.fromJson(json);
      }).toList();
    } catch (e) {
      print('获取VPN配置失败: $e');
      return [];
    }
  }

  /// 保存VPN配置
  Future<bool> saveConfig(VpnConfig config) async {
    try {
      final configs = await getAllConfigs();
      
      // 检查是否已存在相同ID的配置
      final existingIndex = configs.indexWhere((c) => c.id == config.id);
      if (existingIndex != -1) {
        configs[existingIndex] = config;
      } else {
        configs.add(config);
      }
      
      return await _saveConfigs(configs);
    } catch (e) {
      print('保存VPN配置失败: $e');
      return false;
    }
  }

  /// 删除VPN配置
  Future<bool> deleteConfig(String configId) async {
    try {
      final configs = await getAllConfigs();
      configs.removeWhere((config) => config.id == configId);
      
      // 如果删除的是当前活动配置，清除活动配置
      final activeConfig = await getActiveConfig();
      if (activeConfig?.id == configId) {
        await clearActiveConfig();
      }
      
      return await _saveConfigs(configs);
    } catch (e) {
      print('删除VPN配置失败: $e');
      return false;
    }
  }

  /// 设置活动配置
  Future<bool> setActiveConfig(String configId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.setString(_activeConfigKey, configId);
    } catch (e) {
      print('设置活动配置失败: $e');
      return false;
    }
  }

  /// 获取活动配置
  Future<VpnConfig?> getActiveConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final activeConfigId = prefs.getString(_activeConfigKey);
      
      if (activeConfigId == null) return null;
      
      final configs = await getAllConfigs();
      return configs.firstWhere(
        (config) => config.id == activeConfigId,
        orElse: () => throw Exception('未找到活动配置'),
      );
    } catch (e) {
      print('获取活动配置失败: $e');
      return null;
    }
  }

  /// 清除活动配置
  Future<bool> clearActiveConfig() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return await prefs.remove(_activeConfigKey);
    } catch (e) {
      print('清除活动配置失败: $e');
      return false;
    }
  }

  /// 创建新的VPN配置
  VpnConfig createConfig({
    required String name,
    required String serverAddress,
    required int port,
    required String protocol,
    required String username,
    required String password,
    String? certificate,
    String? privateKey,
    String? caCertificate,
    Map<String, dynamic>? additionalSettings,
    required String country,
    required String flagAsset,
  }) {
    return VpnConfig(
      id: const Uuid().v4(),
      name: name,
      serverAddress: serverAddress,
      port: port,
      protocol: protocol,
      username: username,
      password: password,
      certificate: certificate,
      privateKey: privateKey,
      caCertificate: caCertificate,
      additionalSettings: additionalSettings,
      createdAt: DateTime.now(),
      country: country,
      flagAsset: flagAsset,
    );
  }

  /// 导出配置文件
  Future<String?> exportConfig(VpnConfig config, String format) async {
    try {
      final directory = await getApplicationDocumentsDirectory();
      final fileName = '${config.name}_${format.toLowerCase()}.${_getFileExtension(format)}';
      final file = File('${directory.path}/$fileName');
      
      String content;
      switch (format.toUpperCase()) {
        case 'OPENVPN':
          content = config.generateOpenVpnConfig();
          break;
        case 'WIREGUARD':
          content = config.generateWireGuardConfig();
          break;
        case 'JSON':
          content = jsonEncode(config.toJson());
          break;
        default:
          throw Exception('不支持的格式: $format');
      }
      
      await file.writeAsString(content);
      return file.path;
    } catch (e) {
      print('导出配置失败: $e');
      return null;
    }
  }

  /// 导入配置文件
  Future<VpnConfig?> importConfig(String filePath) async {
    try {
      final file = File(filePath);
      if (!await file.exists()) {
        throw Exception('文件不存在');
      }
      
      final content = await file.readAsString();
      final extension = filePath.split('.').last.toLowerCase();
      
      switch (extension) {
        case 'json':
          final json = jsonDecode(content);
          return VpnConfig.fromJson(json);
        case 'ovpn':
          return _parseOpenVpnConfig(content);
        case 'conf':
          return _parseWireGuardConfig(content);
        default:
          throw Exception('不支持的文件格式: $extension');
      }
    } catch (e) {
      print('导入配置失败: $e');
      return null;
    }
  }

  /// 更新配置的最后连接时间
  Future<bool> updateLastConnected(String configId) async {
    try {
      final configs = await getAllConfigs();
      final configIndex = configs.indexWhere((c) => c.id == configId);
      
      if (configIndex == -1) return false;
      
      configs[configIndex] = configs[configIndex].copyWith(
        lastConnected: DateTime.now(),
      );
      
      return await _saveConfigs(configs);
    } catch (e) {
      print('更新最后连接时间失败: $e');
      return false;
    }
  }

  /// 私有方法：保存所有配置
  Future<bool> _saveConfigs(List<VpnConfig> configs) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final configsJson = configs.map((config) => jsonEncode(config.toJson())).toList();
      return await prefs.setStringList(_configsKey, configsJson);
    } catch (e) {
      print('保存配置列表失败: $e');
      return false;
    }
  }

  /// 获取文件扩展名
  String _getFileExtension(String format) {
    switch (format.toUpperCase()) {
      case 'OPENVPN':
        return 'ovpn';
      case 'WIREGUARD':
        return 'conf';
      case 'JSON':
        return 'json';
      default:
        return 'txt';
    }
  }

  /// 解析OpenVPN配置文件
  VpnConfig _parseOpenVpnConfig(String content) {
    // 简化的OpenVPN配置解析
    final lines = content.split('\n');
    String? serverAddress;
    int port = 1194;
    
    for (final line in lines) {
      if (line.startsWith('remote ')) {
        final parts = line.split(' ');
        if (parts.length >= 2) {
          serverAddress = parts[1];
          if (parts.length >= 3) {
            port = int.tryParse(parts[2]) ?? 1194;
          }
        }
      }
    }
    
    return createConfig(
      name: 'Imported OpenVPN',
      serverAddress: serverAddress ?? 'unknown',
      port: port,
      protocol: 'OpenVPN',
      username: '',
      password: '',
      country: 'Unknown',
      flagAsset: 'assets/logo.png',
    );
  }

  /// 解析WireGuard配置文件
  VpnConfig _parseWireGuardConfig(String content) {
    // 简化的WireGuard配置解析
    final lines = content.split('\n');
    String? serverAddress;
    int port = 51820;
    
    for (final line in lines) {
      if (line.startsWith('Endpoint = ')) {
        final endpoint = line.substring(11).trim();
        final parts = endpoint.split(':');
        if (parts.length >= 2) {
          serverAddress = parts[0];
          port = int.tryParse(parts[1]) ?? 51820;
        }
      }
    }
    
    return createConfig(
      name: 'Imported WireGuard',
      serverAddress: serverAddress ?? 'unknown',
      port: port,
      protocol: 'WireGuard',
      username: '',
      password: '',
      country: 'Unknown',
      flagAsset: 'assets/logo.png',
    );
  }
}
