platform :osx, '10.11'

# CocoaPods analytics sends network stats synchronously affecting flutter build latency.
ENV['COCOAPODS_DISABLE_STATS'] = 'true'

project 'Runner', {
  'Debug' => :debug,
  'Profile' => :release,
  'Release' => :release,
}

def parse_KV_file(file, separator='=')
  file_abs_path = File.expand_path(file)
  if !File.exists? file_abs_path
    return [];
  end
  pods_ary = []
  skip_line_start_symbols = ["#", "/"]
  File.foreach(file_abs_path) { |line|
      next if skip_line_start_symbols.any? { |symbol| line =~ /^\s*#{symbol}/ }
      plugin = line.split(pattern=separator)
      if plugin.length == 2
        podname = plugin[0].strip()
        path = plugin[1].strip()
        podpath = File.expand_path("#{path}", file_abs_path)
        pods_ary.push({:name => podname, :path => podpath});
      else
        puts "Invalid plugin specification: #{line}"
      end
  }
  return pods_ary
end

def pubspec_supports_macos(file)
  file_abs_path = File.expand_path(file)
  if !File.exists? file_abs_path
    return false;
  end
  File.foreach(file_abs_path) { |line|
    return true if line =~ /^\s*macos:/
  }
  return false
end

target 'Runner' do
  use_frameworks!
  use_modular_headers!

  # Prepare symlinks folder. We use symlinks to avoid having Podfile.lock
  # referring to absolute paths on developers' machines.
  ephemeral_dir = File.join('Flutter', 'ephemeral')
  symlink_dir = File.join(ephemeral_dir, '.symlinks')
  symlink_plugins_dir = File.join(symlink_dir, 'plugins')
  system("rm -rf #{symlink_dir}")
  system("mkdir -p #{symlink_plugins_dir}")

  # Flutter Pods
  generated_xcconfig = parse_KV_file(File.join(ephemeral_dir, 'Flutter-Generated.xcconfig'))
  if generated_xcconfig.empty?
    puts "Flutter-Generated.xcconfig must exist. If you're running pod install manually, make sure flutter packages get is executed first."
  end
  generated_xcconfig.map { |p|
    if p[:name] == 'FLUTTER_FRAMEWORK_DIR'
      symlink = File.join(symlink_dir, 'flutter')
      File.symlink(File.dirname(p[:path]), symlink)
      pod 'FlutterMacOS', :path => File.join(symlink, File.basename(p[:path]))
    end
  }

  # Plugin Pods
  plugin_pods = parse_KV_file('../.flutter-plugins')
  plugin_pods.map { |p|
    symlink = File.join(symlink_plugins_dir, p[:name])
    File.symlink(p[:path], symlink)
    if pubspec_supports_macos(File.join(symlink, 'pubspec.yaml'))
      pod p[:name], :path => File.join(symlink, 'macos')
    end
  }
end

# Prevent Cocoapods from embedding a second Flutter framework and causing an error with the new Xcode build system.
install! 'cocoapods', :disable_input_output_paths => true
