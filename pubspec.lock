# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.dev"
    source: hosted
    version: "2.10.0"
  avatar_glow:
    dependency: "direct main"
    description:
      name: avatar_glow
      sha256: "3627f6f97d1f10d3c1996ae108d3488eabaf68c0cf0c3eac7e7c746a3812881d"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.2"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  circularbox:
    dependency: "direct main"
    description:
      name: circularbox
      sha256: "0be0fd933fb169e9b23b7f45b6ee71e5133fb7ad6cbb6be20636285f87a0ce53"
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1+1"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.dev"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.dev"
    source: hosted
    version: "1.17.0"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: aa274aa7774f8964e4f4f38cc994db7b6158dd36e9187aaceaddc994b35c6c67
      url: "https://pub.dev"
    source: hosted
    version: "3.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: e35129dc44c9118cee2a5603506d823bab99c68393879edb440e0090d07586be
      url: "https://pub.dev"
    source: hosted
    version: "1.0.5"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: a38574032c5f1dd06c4aee541789906c12ccaab8ba01446e800d9c5b79c4a978
      url: "https://pub.dev"
    source: hosted
    version: "2.0.1"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.dev"
    source: hosted
    version: "6.1.4"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_vpn:
    dependency: "direct main"
    description:
      name: flutter_vpn
      sha256: "0217d5aab740903008e45daf4f18f03e11c97c6a0affef656276f4158f44b540"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.0"
  google_fonts:
    dependency: "direct main"
    description:
      name: google_fonts
      sha256: "927573f2e8a8d65c17931e21918ad0ab0666b1b636537de7c4932bdb487b190f"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.3"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "6aa2946395183537c8b880962d935877325d6a09a2867c3970c05c0fed6ac482"
      url: "https://pub.dev"
    source: hosted
    version: "0.13.5"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.dev"
    source: hosted
    version: "4.0.2"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.dev"
    source: hosted
    version: "0.6.5"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.dev"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.dev"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.dev"
    source: hosted
    version: "1.8.0"
  path:
    dependency: transitive
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.dev"
    source: hosted
    version: "1.8.2"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: c7edf82217d4b2952b2129a61d3ad60f1075b9299e629e149a8d2e39c2e6aad4
      url: "https://pub.dev"
    source: hosted
    version: "2.0.14"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: "019f18c9c10ae370b08dce1f3e3b73bc9f58e7f087bb5e921f06529438ac0ae7"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.24"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "818b2dc38b0f178e0ea3f7cf3b28146faab11375985d815942a68eee11c2d0f7"
      url: "https://pub.dev"
    source: hosted
    version: "2.2.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: "2ae08f2216225427e64ad224a24354221c2c7907e448e6e0e8b57b1eb9f10ad1"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.10"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "57585299a729335f1298b43245842678cb9f43a6310351b18fb577d6e33165ec"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.6"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: f53720498d5a543f9607db4b0e997c4b5438884de25b0f73098cc2671a51b130
      url: "https://pub.dev"
    source: hosted
    version: "2.1.5"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "4a451831508d7d6ca779f7ac6e212b4023dd5a7d08a27a63da33756410e32b76"
      url: "https://pub.dev"
    source: hosted
    version: "3.1.0"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "6a2128648c854906c53fa8e33986fc0247a1116122f9534dd20e3ab9e16a32bc"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  process:
    dependency: transitive
    description:
      name: process
      sha256: "53fd8db9cec1d37b0574e12f07520d582019cb6c44abf5479a01505099a34a09"
      url: "https://pub.dev"
    source: hosted
    version: "4.2.4"
  roundcheckbox:
    dependency: "direct main"
    description:
      name: roundcheckbox
      sha256: "5d6f607ed9d26fde0072af2545f56d84a6b6d2a45972b0c41f90c2d73224f339"
      url: "https://pub.dev"
    source: hosted
    version: "2.0.5"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.dev"
    source: hosted
    version: "1.9.1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.dev"
    source: hosted
    version: "1.11.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.dev"
    source: hosted
    version: "1.2.0"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.dev"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.dev"
    source: hosted
    version: "0.4.16"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: "26f87ade979c47a150c9eaab93ccd2bebe70a27dc0b4b29517f2904f04eb11a5"
      url: "https://pub.dev"
    source: hosted
    version: "1.3.1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.dev"
    source: hosted
    version: "2.1.4"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: c9ebe7ee4ab0c2194e65d3a07d8c54c5d00bb001b76081c4a04cdb8448b59e46
      url: "https://pub.dev"
    source: hosted
    version: "3.1.3"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: ee1505df1426458f7f60aac270645098d318a8b4766d85fde75f76f2e21807d1
      url: "https://pub.dev"
    source: hosted
    version: "1.0.0"
sdks:
  dart: ">=2.19.5 <3.0.0"
  flutter: ">=3.0.0"
