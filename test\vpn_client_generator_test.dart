import 'package:flutter_test/flutter_test.dart';
import 'package:etornam_vpn/models/vpn_config.dart';
import 'package:etornam_vpn/services/vpn_client_generator.dart';
import 'package:etornam_vpn/services/vpn_config_service.dart';

void main() {
  group('VPN客户端生成器测试', () {
    late VpnClientGenerator generator;
    late VpnConfigService configService;

    setUp(() {
      generator = VpnClientGenerator.instance;
      configService = VpnConfigService.instance;
    });

    test('应该能够生成OpenVPN客户端配置', () async {
      final result = await generator.generateClientPackage(
        clientName: '测试客户端',
        serverAddress: 'vpn.example.com',
        port: 1194,
        protocol: 'OpenVPN',
      );

      expect(result['success'], true);
      expect(result['clientInfo'], isNotNull);
      expect(result['vpnConfig'], isNotNull);
      expect(result['configFiles'], isNotNull);
      expect(result['installScripts'], isNotNull);

      final clientInfo = result['clientInfo'];
      expect(clientInfo['clientName'], '测试客户端');
      expect(clientInfo['serverAddress'], 'vpn.example.com');
      expect(clientInfo['port'], 1194);
      expect(clientInfo['protocol'], 'OpenVPN');

      final configFiles = result['configFiles'] as Map<String, String>;
      expect(configFiles.containsKey('client.ovpn'), true);
      expect(configFiles.containsKey('config.json'), true);
      expect(configFiles.containsKey('connect.sh'), true);
      expect(configFiles.containsKey('disconnect.sh'), true);
    });

    test('应该能够生成WireGuard客户端配置', () async {
      final result = await generator.generateClientPackage(
        clientName: 'WireGuard测试',
        serverAddress: '*************',
        port: 51820,
        protocol: 'WireGuard',
      );

      expect(result['success'], true);
      
      final configFiles = result['configFiles'] as Map<String, String>;
      expect(configFiles.containsKey('client.conf'), true);
      
      final clientInfo = result['clientInfo'];
      expect(clientInfo['protocol'], 'WireGuard');
    });

    test('应该能够批量生成客户端配置', () async {
      final clientNames = ['客户端1', '客户端2', '客户端3'];
      
      final results = await generator.generateBatchClients(
        clientNames: clientNames,
        serverAddress: 'vpn.test.com',
        port: 1194,
        protocol: 'OpenVPN',
      );

      expect(results.length, 3);
      
      for (int i = 0; i < results.length; i++) {
        expect(results[i]['success'], true);
        expect(results[i]['clientInfo']['clientName'], clientNames[i]);
      }
    });

    test('应该能够生成QR码配置', () async {
      final config = VpnConfig(
        id: 'test-id',
        name: 'QR测试',
        serverAddress: 'qr.test.com',
        port: 1194,
        protocol: 'OpenVPN',
        username: 'testuser',
        password: 'testpass',
        createdAt: DateTime.now(),
        country: 'Test',
        flagAsset: 'assets/logo.png',
      );

      final qrData = await generator.generateQrConfig(config);
      expect(qrData, isNotEmpty);
      expect(qrData, isA<String>());
    });

    test('应该能够验证客户端配置', () async {
      // 测试有效配置
      final validConfig = VpnConfig(
        id: 'valid-id',
        name: '有效配置',
        serverAddress: 'valid.server.com',
        port: 1194,
        protocol: 'OpenVPN',
        username: 'validuser',
        password: 'validpass',
        createdAt: DateTime.now(),
        country: 'Test',
        flagAsset: 'assets/logo.png',
      );

      final validResult = await generator.validateClientConfig(validConfig);
      expect(validResult['isValid'], true);
      expect(validResult['issues'], isEmpty);

      // 测试无效配置
      final invalidConfig = VpnConfig(
        id: 'invalid-id',
        name: '无效配置',
        serverAddress: '', // 空服务器地址
        port: 70000, // 无效端口
        protocol: 'InvalidProtocol', // 无效协议
        username: '',
        password: '',
        createdAt: DateTime.now(),
        country: 'Test',
        flagAsset: 'assets/logo.png',
      );

      final invalidResult = await generator.validateClientConfig(invalidConfig);
      expect(invalidResult['isValid'], false);
      expect(invalidResult['issues'], isNotEmpty);
    });

    test('VpnConfig模型应该能够正确序列化和反序列化', () {
      final originalConfig = VpnConfig(
        id: 'test-id',
        name: '测试配置',
        serverAddress: 'test.server.com',
        port: 1194,
        protocol: 'OpenVPN',
        username: 'testuser',
        password: 'testpass',
        certificate: 'test-cert',
        privateKey: 'test-key',
        caCertificate: 'test-ca',
        additionalSettings: {'test': 'value'},
        createdAt: DateTime.now(),
        lastConnected: DateTime.now(),
        isActive: true,
        country: 'Test',
        flagAsset: 'assets/test.png',
      );

      // 序列化
      final json = originalConfig.toJson();
      expect(json, isA<Map<String, dynamic>>());

      // 反序列化
      final deserializedConfig = VpnConfig.fromJson(json);
      expect(deserializedConfig.id, originalConfig.id);
      expect(deserializedConfig.name, originalConfig.name);
      expect(deserializedConfig.serverAddress, originalConfig.serverAddress);
      expect(deserializedConfig.port, originalConfig.port);
      expect(deserializedConfig.protocol, originalConfig.protocol);
      expect(deserializedConfig.username, originalConfig.username);
      expect(deserializedConfig.password, originalConfig.password);
    });

    test('应该能够生成OpenVPN配置文件内容', () {
      final config = VpnConfig(
        id: 'test-id',
        name: 'OpenVPN测试',
        serverAddress: 'openvpn.test.com',
        port: 1194,
        protocol: 'OpenVPN',
        username: 'testuser',
        password: 'testpass',
        caCertificate: 'test-ca-cert',
        certificate: 'test-client-cert',
        privateKey: 'test-private-key',
        createdAt: DateTime.now(),
        country: 'Test',
        flagAsset: 'assets/logo.png',
      );

      final ovpnContent = config.generateOpenVpnConfig();
      expect(ovpnContent, contains('client'));
      expect(ovpnContent, contains('dev tun'));
      expect(ovpnContent, contains('proto udp'));
      expect(ovpnContent, contains('remote openvpn.test.com 1194'));
      expect(ovpnContent, contains('<ca>'));
      expect(ovpnContent, contains('<cert>'));
      expect(ovpnContent, contains('<key>'));
    });

    test('应该能够生成WireGuard配置文件内容', () {
      final config = VpnConfig(
        id: 'test-id',
        name: 'WireGuard测试',
        serverAddress: 'wireguard.test.com',
        port: 51820,
        protocol: 'WireGuard',
        username: 'testuser',
        password: 'testpass',
        certificate: 'test-public-key',
        privateKey: 'test-private-key',
        createdAt: DateTime.now(),
        country: 'Test',
        flagAsset: 'assets/logo.png',
      );

      final wgContent = config.generateWireGuardConfig();
      expect(wgContent, contains('[Interface]'));
      expect(wgContent, contains('[Peer]'));
      expect(wgContent, contains('PrivateKey = test-private-key'));
      expect(wgContent, contains('PublicKey = test-public-key'));
      expect(wgContent, contains('Endpoint = wireguard.test.com:51820'));
    });

    test('copyWith方法应该正确工作', () {
      final originalConfig = VpnConfig(
        id: 'test-id',
        name: '原始配置',
        serverAddress: 'original.server.com',
        port: 1194,
        protocol: 'OpenVPN',
        username: 'originaluser',
        password: 'originalpass',
        createdAt: DateTime.now(),
        country: 'Original',
        flagAsset: 'assets/original.png',
      );

      final modifiedConfig = originalConfig.copyWith(
        name: '修改后的配置',
        serverAddress: 'modified.server.com',
        port: 443,
      );

      expect(modifiedConfig.id, originalConfig.id); // 未修改的字段保持不变
      expect(modifiedConfig.name, '修改后的配置'); // 修改的字段更新
      expect(modifiedConfig.serverAddress, 'modified.server.com');
      expect(modifiedConfig.port, 443);
      expect(modifiedConfig.protocol, originalConfig.protocol); // 未修改的字段保持不变
    });
  });
}
